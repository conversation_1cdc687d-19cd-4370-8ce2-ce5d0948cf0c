defmodule ReconciliationWeb.DashboardLive do
  use ReconciliationWeb, :live_view

  alias Reconciliation.Services.ActivityLogger

  @impl true
  def mount(_params, _session, socket) do
    user = socket.assigns.current_user

    # Log page visit
    if connected?(socket) do
      ActivityLogger.log_data_access_activity(
        user.id,
        "page_visit",
        organization_id: user.organization_id,
        metadata: %{
          page: "dashboard",
          url: "/dashboard"
        }
      )
    end

    # Subscribe to reconciliation updates for real-time dashboard updates
    require Logger
    Logger.info("Dashboard subscribing to reconciliation updates for user #{user.id}")
    Phoenix.PubSub.subscribe(Reconciliation.PubSub, "reconciliation_updates:#{user.id}")

    runs = Reconciliation.list_reconciliation_runs(user.id)
    recent_runs = Enum.take(runs, 5)

    # Calculate dynamic summary values from completed runs only
    completed_runs = Enum.filter(runs, &(&1.status == "completed"))

    # Calculate additional analytics from real data
    total_files = runs |> Enum.flat_map(& &1.uploaded_files) |> length()
    total_amount_processed =
      completed_runs
      |> Enum.map(&Decimal.add(&1.total_amount_a || Decimal.new("0"), &1.total_amount_b || Decimal.new("0")))
      |> Enum.reduce(Decimal.new("0"), &Decimal.add/2)

    pending_runs_count = Enum.count(runs, &(&1.status == "pending"))

    {:ok,
     socket
     |> assign(:page_title, "Dashboard")
     |> assign(:runs, runs)
     |> assign(:recent_runs, recent_runs)
     |> assign(:selected_run_id, nil)  # No filter by default (show all)
     |> assign(:total_files, total_files)
     |> assign(:total_amount_processed, total_amount_processed)
     |> assign(:pending_runs_count, pending_runs_count)
     |> assign(:current_path, "/dashboard")
     |> update_dashboard_stats(completed_runs)
    }
  end

  @impl true
  def handle_event("filter_by_run", %{"run_id" => ""}, socket) do
    # Show all runs
    runs = socket.assigns.runs
    completed_runs = Enum.filter(runs, &(&1.status == "completed"))

    {:noreply,
     socket
     |> assign(:selected_run_id, nil)
     |> update_dashboard_stats(completed_runs)
    }
  end

  def handle_event("filter_by_run", %{"run_id" => run_id}, socket) do
    # Filter by specific run
    run_id_int = String.to_integer(run_id)
    runs = socket.assigns.runs
    selected_run = Enum.find(runs, &(&1.id == run_id_int))

    filtered_runs = if selected_run && selected_run.status == "completed" do
      [selected_run]
    else
      []
    end

    {:noreply,
     socket
     |> assign(:selected_run_id, run_id)
     |> update_dashboard_stats(filtered_runs)
    }
  end

  defp update_dashboard_stats(socket, completed_runs) do
    # Calculate separate totals for each data source
    total_processed_a = completed_runs |> Enum.map(&(&1.total_transactions_a || 0)) |> Enum.sum()
    total_processed_b = completed_runs |> Enum.map(&(&1.total_transactions_b || 0)) |> Enum.sum()

    # Get sample file names for display
    all_files = socket.assigns.runs |> Enum.flat_map(& &1.uploaded_files)
    sample_file_a_name = all_files |> Enum.find(&(&1.file_type == "file_a")) |> case do
      nil -> nil
      file -> Reconciliation.UploadedFile.short_display_name(file, 20)
    end
    sample_file_b_name = all_files |> Enum.find(&(&1.file_type == "file_b")) |> case do
      nil -> nil
      file -> Reconciliation.UploadedFile.short_display_name(file, 20)
    end

    # Total transactions should be the sum of ALL individual transactions from both files
    # Each transaction is a separate entity that can be either matched or unmatched
    total_transactions =
      completed_runs
      |> Enum.map(&((&1.total_transactions_a || 0) + (&1.total_transactions_b || 0)))
      |> Enum.sum()

    # Calculate unmatched correctly - sum of unmatched from both files
    total_unmatched_a = completed_runs |> Enum.map(&(&1.unmatched_a_count || 0)) |> Enum.sum()
    total_unmatched_b = completed_runs |> Enum.map(&(&1.unmatched_b_count || 0)) |> Enum.sum()
    total_unmatched = total_unmatched_a + total_unmatched_b

    # Calculate matched individual transactions (total - unmatched)
    total_matched = total_transactions - total_unmatched

    # Calculate overall match rate across all completed runs
    match_rate =
      if total_transactions > 0 do
        Float.round(total_matched / total_transactions * 100, 2)
      else
        0.0
      end

    # Prepare chart data
    chart_data = prepare_chart_data(completed_runs)

    socket
    |> assign(:total_processed_a, total_processed_a)
    |> assign(:total_processed_b, total_processed_b)
    |> assign(:sample_file_a_name, sample_file_a_name)
    |> assign(:sample_file_b_name, sample_file_b_name)
    |> assign(:total_transactions, total_transactions)
    |> assign(:total_matched, total_matched)
    |> assign(:total_unmatched, total_unmatched)
    |> assign(:match_rate, match_rate)
    |> assign(:chart_data, chart_data)
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="container mx-auto px-4 py-8">
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-white">Financial Overview Dashboard</h1>
        <.link navigate={~p"/reconciliation"} class="bg-orange-400 hover:bg-orange-500 text-white px-6 py-3 rounded-lg font-medium flex items-center">
          <.icon name="hero-plus" class="w-5 h-5 mr-2" />
          New Reconciliation
        </.link>
      </div>

      <!-- Filter Section -->
      <div class="mb-6 flex items-center gap-4">
        <form phx-change="filter_by_run" class="flex items-center gap-2">
          <label for="run_id" class="text-white font-medium">Filter by Run:</label>
          <select name="run_id" id="run_id" class="border border-gray-600 rounded px-3 py-2 bg-gray-800 text-white">
            <option value="" selected={is_nil(@selected_run_id)}>All Runs (Overall)</option>
            <%= for run <- @runs do %>
              <%= if run.status == "completed" do %>
                <option value={run.id} selected={@selected_run_id == to_string(run.id)}>
                  <%= run.name %> - <%= Decimal.to_string(run.match_rate, :normal) %>%
                </option>
              <% end %>
            <% end %>
          </select>
        </form>
        <%= if @selected_run_id do %>
          <span class="text-sm text-gray-300">
            Showing data for selected reconciliation run only
          </span>
        <% else %>
          <span class="text-sm text-gray-300">
            Showing aggregated data across all completed runs
          </span>
        <% end %>
      </div>

      <!-- Summary Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8 justify-center">
        <!-- Total Processed A -->
        <div class="bg-gray-800 rounded-lg shadow-md p-6 border border-gray-700 flex justify-center">
          <div class="flex items-center">
            <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center mr-3">
              <.icon name="hero-document" class="w-4 h-4 text-white" />
            </div>
            <div>
              <h2 class="text-sm font-semibold text-gray-300 mb-1">
                <%= if @sample_file_a_name do %>
                  <%= @sample_file_a_name %>
                <% else %>
                  First Files
                <% end %>
              </h2>
              <p class="text-2xl font-bold text-blue-400"><%= format_number(@total_processed_a) %></p>
            </div>
          </div>
        </div>

        <!-- Total Processed B -->
        <div class="bg-gray-800 rounded-lg shadow-md p-6 border border-gray-700 flex justify-center">
          <div class="flex items-center">
            <div class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center mr-3">
              <.icon name="hero-document-duplicate" class="w-4 h-4 text-white" />
            </div>
            <div>
              <h2 class="text-sm font-semibold text-gray-300 mb-1">
                <%= if @sample_file_b_name do %>
                  <%= @sample_file_b_name %>
                <% else %>
                  Second Files
                <% end %>
              </h2>
              <p class="text-2xl font-bold text-green-400"><%= format_number(@total_processed_b) %></p>
            </div>
          </div>
        </div>

        <!-- Combined Total -->
        <div class="bg-gray-800 rounded-lg shadow-md p-6 border border-gray-700 flex flex-col items-center justify-center">
          <h2 class="text-sm font-semibold text-gray-300 mb-2 text-center">Total Transactions</h2>
          <p class="text-2xl font-bold text-white"><%= format_number(@total_transactions) %></p>
        </div>

        <!-- Matching Rate -->
        <div class="bg-gray-800 rounded-lg shadow-md p-6 border border-gray-700 flex flex-col items-center justify-center">
          <h2 class="text-sm font-semibold text-gray-300 mb-2 text-center">Matching Rate</h2>
          <p class="text-2xl font-bold text-green-400"><%= :erlang.float_to_binary(@match_rate, [decimals: 2]) %>%</p>
        </div>

        <!-- Unmatched -->
        <div class="bg-gray-800 rounded-lg shadow-md p-6 border border-gray-700 flex flex-col items-center justify-center">
          <h2 class="text-sm font-semibold text-gray-300 mb-2 text-center">Unmatched</h2>
          <p class="text-2xl font-bold text-red-400"><%= format_number(@total_unmatched) %></p>
        </div>
      </div>





      <!-- Charts Section -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 justify-center">
        <!-- Reconciliation Trends Chart -->
        <div class="bg-gray-800 rounded-lg shadow-md p-4 border border-gray-700 flex flex-col items-center">
          <h2 class="text-lg font-semibold text-white mb-3 text-center">Reconciliation Trends</h2>
          <div class="h-72 flex items-center justify-center w-full">
            <div class="w-full h-full flex items-center justify-center">
              <canvas id="matchingTrendsChart" width="550" height="288" class="w-full h-full"></canvas>
            </div>
          </div>

          <!-- Stats Grid -->
          <div class="mt-4 grid grid-cols-3 gap-3 w-full">
            <div class="text-center p-2 bg-gray-600 rounded">
              <div class="text-xl font-bold text-green-300"><%= :erlang.float_to_binary(@match_rate, [decimals: 2]) %>%</div>
              <div class="text-xs text-gray-100">Avg Match Rate</div>
            </div>
            <div class="text-center p-2 bg-gray-600 rounded">
              <div class="text-xl font-bold text-blue-300"><%= format_number(@total_matched) %></div>
              <div class="text-xs text-gray-100">Total Matched</div>
            </div>
            <div class="text-center p-2 bg-gray-600 rounded">
              <div class="text-xl font-bold text-red-300"><%= format_number(@total_unmatched) %></div>
              <div class="text-xs text-gray-100">Unmatched</div>
            </div>
          </div>

          <!-- Trend Indicator -->
          <%= if length(@chart_data.trends.match_rates) > 1 do %>
            <% first_rate = List.first(@chart_data.trends.match_rates) %>
            <% last_rate = List.last(@chart_data.trends.match_rates) %>
            <% change = last_rate - first_rate %>
            <% first_matched = List.first(@chart_data.trends.matched_counts) %>
            <% last_matched = List.last(@chart_data.trends.matched_counts) %>
            <% matched_change = last_matched - first_matched %>
            <% first_disc = List.first(@chart_data.trends.discrepancy_counts) %>
            <% last_disc = List.last(@chart_data.trends.discrepancy_counts) %>
            <% disc_change = last_disc - first_disc %>


          <% end %>
        </div>

        <!-- Unmatched Categories Chart -->
        <div class="bg-gray-800 rounded-lg shadow-md p-4 border border-gray-700 flex flex-col items-center">
          <h2 class="text-lg font-semibold text-white mb-3 text-center">Unmatched Categories</h2>
          <div class="h-48 flex items-center justify-center w-full">
            <div class="w-48 h-48 flex items-center justify-center">
              <canvas id="discrepancyChart" width="192" height="192" class="w-full h-full"></canvas>
            </div>
          </div>
          <!-- Legend -->
          <div class="mt-4 space-y-2 w-full">
            <div class="flex items-center justify-between p-2 bg-gray-600 rounded text-sm">
              <div class="flex items-center">
                <div class="w-2.5 h-2.5 bg-red-500 rounded-full mr-2"></div>
                <span class="text-gray-100">Amount Mismatch</span>
              </div>
              <div class="text-right">
                <span class="font-bold text-white"><%= @chart_data.discrepancies.amount_mismatch %></span>
                <span class="text-xs text-gray-200 ml-1">(<%= if @total_unmatched > 0, do: Float.round(@chart_data.discrepancies.amount_mismatch / @total_unmatched * 100, 1), else: 0 %>%)</span>
              </div>
            </div>

            <div class="flex items-center justify-between p-2 bg-gray-600 rounded text-sm">
              <div class="flex items-center">
                <div class="w-2.5 h-2.5 bg-orange-500 rounded-full mr-2"></div>
                <span class="text-gray-100">Date Variance</span>
              </div>
              <div class="text-right">
                <span class="font-bold text-white"><%= @chart_data.discrepancies.date_variance %></span>
                <span class="text-xs text-gray-200 ml-1">(<%= if @total_unmatched > 0, do: Float.round(@chart_data.discrepancies.date_variance / @total_unmatched * 100, 1), else: 0 %>%)</span>
              </div>
            </div>

            <div class="flex items-center justify-between p-2 bg-gray-600 rounded text-sm">
              <div class="flex items-center">
                <div class="w-2.5 h-2.5 bg-yellow-500 rounded-full mr-2"></div>
                <span class="text-gray-100">Missing Reference</span>
              </div>
              <div class="text-right">
                <span class="font-bold text-white"><%= @chart_data.discrepancies.missing_reference %></span>
                <span class="text-xs text-gray-200 ml-1">(<%= if @total_unmatched > 0, do: Float.round(@chart_data.discrepancies.missing_reference / @total_unmatched * 100, 1), else: 0 %>%)</span>
              </div>
            </div>

            <div class="flex items-center justify-between p-2 bg-gray-600 rounded text-sm">
              <div class="flex items-center">
                <div class="w-2.5 h-2.5 bg-purple-500 rounded-full mr-2"></div>
                <span class="text-gray-100">Duplicate Entry</span>
              </div>
              <div class="text-right">
                <span class="font-bold text-white"><%= @chart_data.discrepancies.duplicate_entry %></span>
                <span class="text-xs text-gray-200 ml-1">(<%= if @total_unmatched > 0, do: Float.round(@chart_data.discrepancies.duplicate_entry / @total_unmatched * 100, 1), else: 0 %>%)</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Chart.js Script -->
      <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
      <script>
        window.chartData = <%= raw Jason.encode!(@chart_data) %>;

        document.addEventListener('DOMContentLoaded', function() {
          // Matching Trends Line Chart with Multiple Lines
          const matchingCtx = document.getElementById('matchingTrendsChart');
          if (matchingCtx) {
            new Chart(matchingCtx, {
              type: 'line',
              data: {
                labels: window.chartData.trends.labels,
                datasets: [
                  {
                    label: 'Match Rate %',
                    data: window.chartData.trends.match_rates,
                    borderColor: '#10b981',
                    backgroundColor: 'transparent',
                    borderWidth: 3,
                    fill: false,
                    tension: 0,
                    pointBackgroundColor: '#10b981',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 8,
                    pointHoverRadius: 10,
                    yAxisID: 'y'
                  },
                  {
                    label: 'Matched Count',
                    data: window.chartData.trends.matched_counts,
                    borderColor: '#3b82f6',
                    backgroundColor: 'transparent',
                    borderWidth: 3,
                    fill: false,
                    tension: 0,
                    pointBackgroundColor: '#3b82f6',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 8,
                    pointHoverRadius: 10,
                    yAxisID: 'y1'
                  },
                  {
                    label: 'Unmatched',
                    data: window.chartData.trends.discrepancy_counts,
                    borderColor: '#ef4444',
                    backgroundColor: 'transparent',
                    borderWidth: 3,
                    fill: false,
                    tension: 0,
                    pointBackgroundColor: '#ef4444',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 8,
                    pointHoverRadius: 10,
                    yAxisID: 'y1'
                  }
                ]
              },
              options: {
                responsive: true,
                maintainAspectRatio: false,
                devicePixelRatio: 2,
                layout: {
                  padding: {
                    top: 20,
                    right: 25,
                    bottom: 20,
                    left: 30
                  }
                },
                interaction: {
                  mode: 'index',
                  intersect: false
                },
                plugins: {
                  legend: {
                    display: true,
                    position: 'top',
                    labels: {
                      color: '#d1d5db',
                      usePointStyle: true,
                      pointStyle: 'circle',
                      padding: 20
                    }
                  },
                  tooltip: {
                    backgroundColor: 'rgba(17, 24, 39, 0.95)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#374151',
                    borderWidth: 1,
                    cornerRadius: 8
                  }
                },
                scales: {
                  y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    beginAtZero: true,
                    min: 0,
                    max: 100,
                    grid: {
                      color: 'rgba(75, 85, 99, 0.2)',
                      drawBorder: false
                    },
                    ticks: {
                      color: '#10b981',
                      stepSize: 10,
                      padding: 12,
                      font: { size: 16, weight: 'bold' },
                      callback: function(value) { return value + '%'; }
                    },
                    title: {
                      display: true,
                      text: 'Match Rate (%)',
                      color: '#10b981',
                      font: { size: 14, weight: 'bold' }
                    }
                  },
                  y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    beginAtZero: true,
                    grid: { drawOnChartArea: false },
                    ticks: {
                      color: '#3b82f6',
                      padding: 12,
                      font: { size: 16, weight: 'bold' },
                      callback: function(value) { return Math.round(value); }
                    },
                    title: {
                      display: true,
                      text: 'Transaction Count',
                      color: '#3b82f6',
                      font: { size: 14, weight: 'bold' }
                    }
                  },
                  x: {
                    grid: {
                      color: 'rgba(75, 85, 99, 0.2)',
                      drawBorder: false
                    },
                    ticks: {
                      color: '#d1d5db',
                      font: { size: 16, weight: 'bold' },
                      maxTicksLimit: 4
                    },
                    bounds: 'data',
                    offset: false
                  }
                }
              }
            });
          }

          // Unmatched Categories Doughnut Chart
          const discrepancyCtx = document.getElementById('discrepancyChart');
          if (discrepancyCtx) {
            new Chart(discrepancyCtx, {
              type: 'doughnut',
              data: {
                labels: ['Amount Mismatch', 'Date Variance', 'Missing Reference', 'Duplicate Entry'],
                datasets: [{
                  data: [
                    window.chartData.discrepancies.amount_mismatch,
                    window.chartData.discrepancies.date_variance,
                    window.chartData.discrepancies.missing_reference,
                    window.chartData.discrepancies.duplicate_entry
                  ],
                  backgroundColor: ['#ef4444', '#f97316', '#eab308', '#a855f7'],
                  borderWidth: 0
                }]
              },
              options: {
                responsive: true,
                maintainAspectRatio: true,
                aspectRatio: 1,
                devicePixelRatio: 2,
                layout: {
                  padding: {
                    top: 10,
                    right: 10,
                    bottom: 10,
                    left: 10
                  }
                },
                plugins: {
                  legend: { display: false }
                },
                elements: {
                  arc: {
                    borderWidth: 0,
                    borderAlign: 'center'
                  }
                },
                animation: {
                  animateRotate: true,
                  animateScale: false
                }
              }
            });
          }
        });
      </script>

      <!-- Additional Analytics -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8 justify-center">
        <div class="bg-white rounded-lg shadow-md p-6 flex justify-center">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-blue-100 text-blue-600">
              <.icon name="hero-clock" class="w-6 h-6" />
            </div>
            <div class="ml-4">
              <h3 class="text-sm font-medium text-gray-500">Avg Processing Time</h3>
              <p class="text-2xl font-semibold text-gray-900">-</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 flex justify-center">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-purple-100 text-purple-600">
              <.icon name="hero-document-check" class="w-6 h-6" />
            </div>
            <div class="ml-4">
              <h3 class="text-sm font-medium text-gray-500">Files Processed</h3>
              <p class="text-2xl font-semibold text-gray-900"><%= format_number(@total_files) %></p>
            </div>
          </div>
        </div>
        <div class="bg-white rounded-lg shadow-md p-6 flex justify-center">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-orange-100 text-orange-400">
              <.icon name="hero-exclamation-triangle" class="w-6 h-6" />
            </div>
            <div class="ml-4">
              <h3 class="text-sm font-medium text-gray-500">Pending Review</h3>
              <p class="text-2xl font-semibold text-gray-900"><%= format_number(@pending_runs_count) %></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Activity / Overview -->
      <div class="bg-gray-800 rounded-lg shadow-md p-6 mb-8 border border-gray-700">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl font-semibold text-white">Recent Reconciliation Runs</h2>
          <.link navigate={~p"/reconciliation"} class="text-orange-300 hover:text-orange-200 font-medium">
            View All →
          </.link>
        </div>

        <%= if @recent_runs && Enum.any?(@recent_runs) do %>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-100">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Name</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Date</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Status</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Match Rate</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <%= for run <- @recent_runs do %>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      <%= run.name %>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                      <%= if run.processed_at do %>
                        <%= Calendar.strftime(run.processed_at, "%b %d, %Y") %>
                      <% else %>
                        <%= Calendar.strftime(run.inserted_at, "%b %d, %Y") %>
                      <% end %>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class={[
                        "inline-flex px-2 py-1 text-xs font-semibold rounded-full",
                        case run.status do
                          "completed" -> "bg-green-100 text-green-800"
                          "processing" -> "bg-yellow-100 text-yellow-800"
                          "failed" -> "bg-red-100 text-red-800"
                          _ -> "bg-gray-100 text-gray-800"
                        end
                      ]}>
                        <%= String.capitalize(run.status) %>
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                      <%= if run.match_rate && Decimal.compare(run.match_rate, Decimal.new("0")) == :gt do %>
                        <%= Decimal.to_string(run.match_rate, :normal) %>%
                      <% else %>
                        0.00%
                      <% end %>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <%= if run.status == "completed" do %>
                        <.link navigate={~p"/reconciliation/#{run.id}/results"} class="text-orange-300 hover:text-orange-200">
                          View Results
                        </.link>
                      <% else %>
                        <span class="text-gray-500">Processing...</span>
                      <% end %>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
        <% else %>
          <div class="text-center py-8">
            <.icon name="hero-document-text" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p class="text-gray-600 mb-4">No reconciliation runs yet</p>
            <.link navigate={~p"/reconciliation"} class="bg-orange-400 hover:bg-orange-500 text-white px-4 py-2 rounded-lg font-medium">
              Start Your First Reconciliation
            </.link>
          </div>
        <% end %>
      </div>

    </div>
    """
  end

  @impl true
  def handle_info({:reconciliation_completed, reconciliation_run_id}, socket) do
    require Logger
    Logger.info("Dashboard received reconciliation completion notification for run #{reconciliation_run_id}")

    # Refresh the dashboard data when a reconciliation completes
    user = socket.assigns.current_user
    runs = Reconciliation.list_reconciliation_runs(user.id)
    recent_runs = Enum.take(runs, 5)

    # Preserve the current filter state
    selected_run_id = socket.assigns.selected_run_id

    # Calculate dynamic summary values based on current filter
    completed_runs = Enum.filter(runs, &(&1.status == "completed"))
    filtered_runs = if selected_run_id do
      run_id_int = String.to_integer(selected_run_id)
      selected_run = Enum.find(completed_runs, &(&1.id == run_id_int))
      if selected_run, do: [selected_run], else: []
    else
      completed_runs
    end

    # Calculate additional analytics from real data
    total_files = runs |> Enum.flat_map(& &1.uploaded_files) |> length()
    total_amount_processed =
      completed_runs
      |> Enum.map(&Decimal.add(&1.total_amount_a || Decimal.new("0"), &1.total_amount_b || Decimal.new("0")))
      |> Enum.reduce(Decimal.new("0"), &Decimal.add/2)

    pending_runs_count = Enum.count(runs, &(&1.status == "pending"))

    {:noreply,
      socket
      |> assign(:runs, runs)
      |> assign(:recent_runs, recent_runs)
      |> assign(:total_files, total_files)
      |> assign(:total_amount_processed, total_amount_processed)
      |> assign(:pending_runs_count, pending_runs_count)
      |> update_dashboard_stats(filtered_runs)
    }
  end

  # Handle other PubSub messages
  def handle_info(_msg, socket) do
    {:noreply, socket}
  end

  # Helper functions
  defp prepare_chart_data(completed_runs) do
    # Sort runs by date for trends
    sorted_runs = Enum.sort_by(completed_runs, & &1.processed_at || &1.inserted_at, DateTime)

    # Prepare matching trends data (last 6 runs or months)
    trends_data = prepare_trends_data(sorted_runs)

    # Prepare unmatched categories data
    discrepancy_data = prepare_discrepancy_data(completed_runs)

    %{
      trends: trends_data,
      discrepancies: discrepancy_data
    }
  end

  defp prepare_trends_data(sorted_runs) do
    # Debug: Log the runs we're working with
    require Logger
    Logger.info("Preparing trends data for #{length(sorted_runs)} runs")

    if Enum.empty?(sorted_runs) do
      Logger.info("No completed runs found for trends chart")
      %{
        labels: [],
        match_rates: [],
        matched_counts: [],
        discrepancy_counts: []
      }
    else
      # Take last 6 runs or all if less than 6
      recent_runs = Enum.take(sorted_runs, -6)

      # Create labels and data points
      {labels, match_rates, matched_counts, discrepancy_counts} =
        recent_runs
        |> Enum.with_index()
        |> Enum.reduce({[], [], [], []}, fn {run, _index}, {labels_acc, rates_acc, matched_acc, disc_acc} ->
          label = if run.processed_at do
            Calendar.strftime(run.processed_at, "%b %d")
          else
            Calendar.strftime(run.inserted_at, "%b %d")
          end

          match_rate = if run.match_rate do
            Decimal.to_float(run.match_rate)
          else
            0.0
          end

          matched = run.matched_count || 0
          unmatched_a = run.unmatched_a_count || 0
          unmatched_b = run.unmatched_b_count || 0
          unmatched = unmatched_a + unmatched_b

          {
            [label | labels_acc],
            [match_rate | rates_acc],
            [matched | matched_acc],
            [unmatched | disc_acc]
          }
        end)

      %{
        labels: Enum.reverse(labels),
        match_rates: Enum.reverse(match_rates),
        matched_counts: Enum.reverse(matched_counts),
        discrepancy_counts: Enum.reverse(discrepancy_counts)
      }
    end
  end

  defp prepare_discrepancy_data(completed_runs) do
    # Calculate total unmatched by type using the correct unmatched counts
    total_unmatched_a = completed_runs |> Enum.map(&(&1.unmatched_a_count || 0)) |> Enum.sum()
    total_unmatched_b = completed_runs |> Enum.map(&(&1.unmatched_b_count || 0)) |> Enum.sum()
    total_unmatched = total_unmatched_a + total_unmatched_b

    # Simulate unmatched categories (you can enhance this with real categorization later)
    if total_unmatched > 0 do
      %{
        amount_mismatch: round(total_unmatched * 0.4),
        date_variance: round(total_unmatched * 0.3),
        missing_reference: round(total_unmatched * 0.2),
        duplicate_entry: round(total_unmatched * 0.1)
      }
    else
      %{
        amount_mismatch: 0,
        date_variance: 0,
        missing_reference: 0,
        duplicate_entry: 0
      }
    end
  end

  defp format_number(number) when is_nil(number), do: "0"
  defp format_number(number) do
    # Simple number formatting without external dependencies
    number
    |> to_string()
    |> String.reverse()
    |> String.replace(~r/(\d{3})(?=\d)/, "\\1,")
    |> String.reverse()
  end

  defp format_currency(amount) when is_nil(amount), do: "0.00"
  defp format_currency(amount) do
    Decimal.to_string(amount, :normal)
  end

  defp format_currency_with_symbol(amount, currency) when is_nil(amount), do: "#{currency_symbol(currency)}0.00"
  defp format_currency_with_symbol(amount, currency) do
    "#{currency_symbol(currency)}#{Decimal.to_string(amount, :normal)}"
  end

  defp currency_symbol(currency) when is_binary(currency) do
    case String.upcase(currency) do
      "USD" -> "$"
      "EUR" -> "€"
      "GBP" -> "£"
      "JPY" -> "¥"
      "CAD" -> "C$"
      "AUD" -> "A$"
      "MWK" -> "MK "
      "ZAR" -> "R "
      "KES" -> "KSh "
      "TZS" -> "TSh "
      "UGX" -> "USh "
      "ZMW" -> "ZK "
      "BWP" -> "P "
      "NAD" -> "N$ "
      "SZL" -> "L "
      "LSL" -> "M "
      other -> "#{other} "
    end
  end
  defp currency_symbol(_), do: ""
end
